import { Injectable, inject } from '@angular/core';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import {
  FieldPermissionModalComponent,
  FieldPermissionModalData,
  FieldPermissionModalResult
} from './field-permission-modal.component';
import { Profile } from '@/shared/components/dynamic-layout-builder/models/field-permission.model';

/**
 * Service để mở modal thiết lập quyền truy cập field
 */
@Injectable({
  providedIn: 'root'
})
export class FieldPermissionModalService {
  private responsiveModalService = inject(ResponsiveModalService);

  /**
   * Mở modal thiết lập quyền truy cập field
   * @param data Dữ liệu đầu vào cho modal
   * @returns Promise<Profile[] | undefined> Danh sách profiles đã cập nhật hoặc undefined nếu hủy
   */
  async open(data: FieldPermissionModalData): Promise<FieldPermissionModalResult> {
    try {
      const modalConfig = {
        data,
        width: '800px',
        maxWidth: '95vw',
        disableClose: false,
        panelClass: 'field-permission-modal'
      };

      const result = await this.responsiveModalService.open<
        FieldPermissionModalData,
        FieldPermissionModalResult,
        FieldPermissionModalComponent
      >(FieldPermissionModalComponent, modalConfig);

      return result;
    } catch (error) {
      console.error('Lỗi khi mở modal thiết lập quyền field:', error);
      return undefined;
    }
  }

  /**
   * Mở modal với dữ liệu mặc định cho testing
   * @param fieldName Tên field
   * @param profileCount Số lượng profiles để tạo (mặc định 3)
   * @returns Promise<Profile[] | undefined> Danh sách profiles đã cập nhật hoặc undefined nếu hủy
   */
  async openWithMockData(fieldName: string = 'Sample Field', profileCount: number = 3): Promise<FieldPermissionModalResult> {
    // Tạo dữ liệu mock cho testing
    const mockProfiles: Profile[] = Array.from({ length: profileCount }, (_, index) => ({
      _id: `profile_${index + 1}`,
      name: `Profile ${index + 1}`,
      permission: index === 0 ? 'read_write' : index === 1 ? 'read' : 'none'
    }));

    const mockData: FieldPermissionModalData = {
      fieldName,
      profiles: mockProfiles
    };

    return this.open(mockData);
  }
}
